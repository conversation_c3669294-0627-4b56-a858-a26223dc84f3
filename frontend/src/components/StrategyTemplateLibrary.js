import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Modal, 
  Input, 
  Select, 
  Tag, 
  Space, 
  Typography,
  Divider,
  Rate,
  Avatar,
  Tooltip,
  message
} from 'antd';
import { 
  FileTextOutlined, 
  EyeOutlined, 
  DownloadOutlined,
  StarOutlined,
  UserOutlined,
  CalendarOutlined,
  CodeOutlined,
  TrophyOutlined,
  RocketOutlined,
  BulbOutlined
} from '@ant-design/icons';

const { Search } = Input;
const { Option } = Select;
const { Text, Paragraph, Title } = Typography;

/**
 * 策略模板库组件
 * 
 * 功能特性：
 * - 策略模板浏览
 * - 模板分类筛选
 * - 模板搜索
 * - 模板预览
 * - 模板使用
 * - 模板评分
 */
const StrategyTemplateLibrary = ({ onUseTemplate, visible, onClose }) => {
  const [templates, setTemplates] = useState([]);
  const [filteredTemplates, setFilteredTemplates] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [previewTemplate, setPreviewTemplate] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);

  /**
   * 策略模板数据
   */
  const strategyTemplates = [
    {
      id: 'ma_cross',
      name: '双均线交叉策略',
      description: '基于短期和长期移动平均线交叉的经典策略，适合趋势跟踪',
      category: 'trend',
      difficulty: 'beginner',
      rating: 4.5,
      downloads: 1250,
      author: '量化专家',
      createTime: '2024-01-15',
      tags: ['移动平均', '趋势跟踪', '经典策略'],
      code: `def initialize(context):
    """双均线交叉策略初始化"""
    context.symbol = '000001.SZ'
    context.short_window = 5
    context.long_window = 20
    
    # 设置基准
    context.benchmark = '000300.SH'
    
    # 设置手续费
    set_order_cost(OrderCost(close_tax=0.001, open_commission=0.0003,
                            close_commission=0.0003, min_commission=5), type='stock')

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.long_window)
    
    # 计算移动平均线
    short_ma = sma(prices[context.symbol], context.short_window)
    long_ma = sma(prices[context.symbol], context.long_window)
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    
    # 交易逻辑
    if short_ma[-1] > long_ma[-1] and current_position == 0:
        # 金叉买入
        order_value(context.symbol, 10000)
        log.info(f"买入信号: 短期均线{short_ma[-1]:.2f} > 长期均线{long_ma[-1]:.2f}")
    elif short_ma[-1] < long_ma[-1] and current_position > 0:
        # 死叉卖出
        order_target_shares(context.symbol, 0)
        log.info(f"卖出信号: 短期均线{short_ma[-1]:.2f} < 长期均线{long_ma[-1]:.2f}")
    
    # 记录数据
    record(short_ma=short_ma[-1], long_ma=long_ma[-1])`,
      performance: {
        annualReturn: '15.2%',
        sharpeRatio: '1.35',
        maxDrawdown: '8.5%',
        winRate: '62%'
      }
    },
    {
      id: 'rsi_reversal',
      name: 'RSI反转策略',
      description: '基于RSI指标的超买超卖反转策略，适合震荡市场',
      category: 'reversal',
      difficulty: 'intermediate',
      rating: 4.2,
      downloads: 890,
      author: '技术分析师',
      createTime: '2024-01-20',
      tags: ['RSI', '反转', '超买超卖'],
      code: `def initialize(context):
    """RSI反转策略初始化"""
    context.symbol = '000001.SZ'
    context.rsi_window = 14
    context.rsi_upper = 70
    context.rsi_lower = 30
    context.position_size = 0.3  # 仓位大小

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.rsi_window + 1)
    
    # 计算RSI指标
    rsi_value = rsi(prices[context.symbol], context.rsi_window)
    current_rsi = rsi_value[-1]
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    total_value = get_portfolio().total_value
    
    # 交易逻辑
    if current_rsi < context.rsi_lower and current_position == 0:
        # RSI超卖，买入
        order_value(context.symbol, total_value * context.position_size)
        log.info(f"RSI超卖买入信号: RSI={current_rsi:.2f}")
    elif current_rsi > context.rsi_upper and current_position > 0:
        # RSI超买，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"RSI超买卖出信号: RSI={current_rsi:.2f}")
    
    # 记录数据
    record(rsi=current_rsi)`,
      performance: {
        annualReturn: '12.8%',
        sharpeRatio: '1.18',
        maxDrawdown: '12.3%',
        winRate: '58%'
      }
    },
    {
      id: 'bollinger_bands',
      name: '布林带策略',
      description: '基于布林带的突破和回归策略，适合波动性交易',
      category: 'volatility',
      difficulty: 'intermediate',
      rating: 4.0,
      downloads: 675,
      author: '波动率专家',
      createTime: '2024-01-25',
      tags: ['布林带', '波动率', '突破'],
      code: `def initialize(context):
    """布林带策略初始化"""
    context.symbol = '000001.SZ'
    context.window = 20
    context.std_dev = 2
    context.position_size = 0.5

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.window)
    current_price = get_current_price(context.symbol)
    
    # 计算布林带
    upper_band, middle_band, lower_band = bollinger_bands(
        prices[context.symbol], context.window, context.std_dev
    )
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    total_value = get_portfolio().total_value
    
    # 交易逻辑
    if current_price < lower_band[-1] and current_position == 0:
        # 价格跌破下轨，买入
        order_value(context.symbol, total_value * context.position_size)
        log.info(f"布林带下轨买入: 价格{current_price:.2f} < 下轨{lower_band[-1]:.2f}")
    elif current_price > upper_band[-1] and current_position > 0:
        # 价格突破上轨，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"布林带上轨卖出: 价格{current_price:.2f} > 上轨{upper_band[-1]:.2f}")
    
    # 记录数据
    record(price=current_price, upper=upper_band[-1], 
           middle=middle_band[-1], lower=lower_band[-1])`,
      performance: {
        annualReturn: '18.5%',
        sharpeRatio: '1.42',
        maxDrawdown: '15.2%',
        winRate: '55%'
      }
    },
    {
      id: 'momentum',
      name: '动量策略',
      description: '基于价格动量的趋势跟踪策略，适合强势股票',
      category: 'momentum',
      difficulty: 'advanced',
      rating: 4.3,
      downloads: 520,
      author: '动量交易员',
      createTime: '2024-02-01',
      tags: ['动量', '趋势', '强势股'],
      code: `def initialize(context):
    """动量策略初始化"""
    context.symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
    context.momentum_window = 20
    context.top_n = 2  # 选择动量最强的N只股票
    context.rebalance_freq = 5  # 每5天调仓一次
    context.day_count = 0

def handle_data(context, data):
    """策略主逻辑"""
    context.day_count += 1
    
    # 每N天调仓一次
    if context.day_count % context.rebalance_freq != 0:
        return
    
    # 计算各股票的动量
    momentum_scores = {}
    for symbol in context.symbols:
        prices = get_history([symbol], 'close', context.momentum_window)
        if len(prices) >= context.momentum_window:
            # 计算动量得分（价格变化率）
            momentum = (prices[symbol][-1] / prices[symbol][0] - 1) * 100
            momentum_scores[symbol] = momentum
    
    # 选择动量最强的股票
    sorted_stocks = sorted(momentum_scores.items(), 
                          key=lambda x: x[1], reverse=True)
    selected_stocks = [stock[0] for stock in sorted_stocks[:context.top_n]]
    
    # 清仓所有持仓
    for symbol in context.symbols:
        order_target_shares(symbol, 0)
    
    # 等权重买入选中的股票
    total_value = get_portfolio().total_value
    position_value = total_value / len(selected_stocks)
    
    for symbol in selected_stocks:
        order_value(symbol, position_value)
        log.info(f"买入动量股票: {symbol}, 动量得分: {momentum_scores[symbol]:.2f}%")
    
    # 记录数据
    record(selected_count=len(selected_stocks))`,
      performance: {
        annualReturn: '22.1%',
        sharpeRatio: '1.58',
        maxDrawdown: '18.7%',
        winRate: '48%'
      }
    },
    {
      id: 'mean_reversion',
      name: '均值回归策略',
      description: '基于价格均值回归的策略，适合稳定的大盘股',
      category: 'reversal',
      difficulty: 'beginner',
      rating: 3.8,
      downloads: 430,
      author: '稳健投资者',
      createTime: '2024-02-05',
      tags: ['均值回归', '稳健', '大盘股'],
      code: `def initialize(context):
    """均值回归策略初始化"""
    context.symbol = '000300.SH'  # 沪深300ETF
    context.window = 30
    context.threshold = 0.02  # 偏离阈值2%

def handle_data(context, data):
    """策略主逻辑"""
    # 获取历史价格数据
    prices = get_history([context.symbol], 'close', context.window)
    current_price = get_current_price(context.symbol)
    
    # 计算移动平均价格
    mean_price = prices[context.symbol].mean()
    
    # 计算价格偏离度
    deviation = (current_price - mean_price) / mean_price
    
    # 获取当前持仓
    current_position = get_portfolio().positions[context.symbol].amount
    
    # 交易逻辑
    if deviation < -context.threshold and current_position == 0:
        # 价格低于均值，买入
        order_value(context.symbol, get_portfolio().total_value * 0.8)
        log.info(f"均值回归买入: 偏离度{deviation:.2%}")
    elif deviation > context.threshold and current_position > 0:
        # 价格高于均值，卖出
        order_target_shares(context.symbol, 0)
        log.info(f"均值回归卖出: 偏离度{deviation:.2%}")
    
    # 记录数据
    record(price=current_price, mean_price=mean_price, deviation=deviation)`,
      performance: {
        annualReturn: '9.5%',
        sharpeRatio: '0.95',
        maxDrawdown: '6.8%',
        winRate: '65%'
      }
    }
  ];

  /**
   * 组件挂载时初始化数据
   */
  useEffect(() => {
    setTemplates(strategyTemplates);
    setFilteredTemplates(strategyTemplates);
  }, []);

  /**
   * 筛选模板
   */
  useEffect(() => {
    let filtered = templates;

    // 按分类筛选
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // 按关键字搜索
    if (searchKeyword) {
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        template.description.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchKeyword.toLowerCase()))
      );
    }

    setFilteredTemplates(filtered);
  }, [templates, selectedCategory, searchKeyword]);

  /**
   * 获取分类图标
   */
  const getCategoryIcon = (category) => {
    const icons = {
      trend: <RocketOutlined />,
      reversal: <BulbOutlined />,
      momentum: <TrophyOutlined />,
      volatility: <CodeOutlined />
    };
    return icons[category] || <FileTextOutlined />;
  };

  /**
   * 获取难度颜色
   */
  const getDifficultyColor = (difficulty) => {
    const colors = {
      beginner: 'green',
      intermediate: 'orange',
      advanced: 'red'
    };
    return colors[difficulty] || 'default';
  };

  /**
   * 获取难度文本
   */
  const getDifficultyText = (difficulty) => {
    const texts = {
      beginner: '初级',
      intermediate: '中级',
      advanced: '高级'
    };
    return texts[difficulty] || difficulty;
  };

  /**
   * 预览模板
   */
  const handlePreview = (template) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  /**
   * 使用模板
   */
  const handleUseTemplate = (template) => {
    if (onUseTemplate) {
      onUseTemplate(template);
      message.success(`已应用模板：${template.name}`);
      onClose();
    }
  };

  return (
    <Modal
      title="策略模板库"
      visible={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
      bodyStyle={{ padding: '20px' }}
    >
      {/* 搜索和筛选 */}
      <Row gutter={16} style={{ marginBottom: '20px' }}>
        <Col span={12}>
          <Search
            placeholder="搜索策略模板..."
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            allowClear
          />
        </Col>
        <Col span={12}>
          <Select
            style={{ width: '100%' }}
            value={selectedCategory}
            onChange={setSelectedCategory}
          >
            <Option value="all">全部分类</Option>
            <Option value="trend">趋势跟踪</Option>
            <Option value="reversal">反转策略</Option>
            <Option value="momentum">动量策略</Option>
            <Option value="volatility">波动率策略</Option>
          </Select>
        </Col>
      </Row>

      {/* 模板列表 */}
      <Row gutter={[16, 16]}>
        {filteredTemplates.map(template => (
          <Col span={12} key={template.id}>
            <Card
              size="small"
              title={
                <Space>
                  {getCategoryIcon(template.category)}
                  <Text strong>{template.name}</Text>
                  <Tag color={getDifficultyColor(template.difficulty)}>
                    {getDifficultyText(template.difficulty)}
                  </Tag>
                </Space>
              }
              extra={
                <Space>
                  <Rate disabled defaultValue={template.rating} allowHalf />
                  <Text type="secondary">({template.downloads})</Text>
                </Space>
              }
              actions={[
                <Tooltip title="预览代码">
                  <Button 
                    type="link" 
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(template)}
                  >
                    预览
                  </Button>
                </Tooltip>,
                <Tooltip title="使用模板">
                  <Button 
                    type="link" 
                    icon={<DownloadOutlined />}
                    onClick={() => handleUseTemplate(template)}
                  >
                    使用
                  </Button>
                </Tooltip>
              ]}
            >
              <Paragraph ellipsis={{ rows: 2 }}>
                {template.description}
              </Paragraph>
              
              <Space wrap>
                {template.tags.map(tag => (
                  <Tag key={tag} size="small">{tag}</Tag>
                ))}
              </Space>
              
              <Divider style={{ margin: '12px 0' }} />
              
              <Row gutter={16}>
                <Col span={12}>
                  <Space>
                    <Avatar size="small" icon={<UserOutlined />} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.author}
                    </Text>
                  </Space>
                </Col>
                <Col span={12}>
                  <Space>
                    <CalendarOutlined style={{ fontSize: '12px' }} />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {template.createTime}
                    </Text>
                  </Space>
                </Col>
              </Row>
              
              <Row gutter={8} style={{ marginTop: '8px' }}>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    年化收益: {template.performance.annualReturn}
                  </Text>
                </Col>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    夏普: {template.performance.sharpeRatio}
                  </Text>
                </Col>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    回撤: {template.performance.maxDrawdown}
                  </Text>
                </Col>
                <Col span={6}>
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    胜率: {template.performance.winRate}
                  </Text>
                </Col>
              </Row>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 模板预览模态框 */}
      <Modal
        title={previewTemplate?.name}
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="use" 
            type="primary"
            onClick={() => {
              handleUseTemplate(previewTemplate);
              setPreviewVisible(false);
            }}
          >
            使用此模板
          </Button>
        ]}
      >
        {previewTemplate && (
          <div>
            <Paragraph>{previewTemplate.description}</Paragraph>
            
            <Title level={5}>策略代码：</Title>
            <pre style={{
              backgroundColor: '#f6f8fa',
              padding: '16px',
              borderRadius: '6px',
              overflow: 'auto',
              maxHeight: '400px',
              fontSize: '12px',
              lineHeight: '1.5'
            }}>
              {previewTemplate.code}
            </pre>
            
            <Title level={5}>性能指标：</Title>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic 
                  title="年化收益率" 
                  value={previewTemplate.performance.annualReturn} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="夏普比率" 
                  value={previewTemplate.performance.sharpeRatio} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="最大回撤" 
                  value={previewTemplate.performance.maxDrawdown} 
                />
              </Col>
              <Col span={6}>
                <Statistic 
                  title="胜率" 
                  value={previewTemplate.performance.winRate} 
                />
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </Modal>
  );
};

export default StrategyTemplateLibrary;
